name: Create or Update an environment
run-name: >
  Create or Update ${{ inputs.shortName != '' && inputs.shortName || format('pr-{0}-{1}', github.event.pull_request.number, github.event.pull_request.user.login) }}

on:
  pull_request:
    types: [opened, reopened, edited]
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: "Environment"
        required: true
        default: "development"
      shortName:
        type: string
        description: "staging, prod or any other short name used as prefix for resources, labels, etc."
        required: true
        default: "PR"
      seedDev:
        type: boolean
        description: "Seed database with dev data"
        required: true
        default: true

jobs:
  deploy:
    name: Create a new environment
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    env:
      DB_INSTANCE_CONNECTION_NAME: "${{ vars.GCP_PROJECT_ID }}:${{ vars.GCP_REGION }}:${{ vars.DB_INSTANCE }}"
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Use Node.js from .nvmrc
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Configure Docker
        run: gcloud auth configure-docker "${{ vars.GCP_REGION }}-docker.pkg.dev"

      - name: Set dynamic ENV
        run: |
          # If inputs.shortName is not present, it means its a PR.
          INPUTS_SHORT_NAME="${{ inputs.shortName || 'PR' }}"

          if [[ "${INPUTS_SHORT_NAME}" == "prod" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="latest"
            FE_URL="https://naturesnectar.palmyra.pro"

            echo "FE_URL=${TMP_PREVIEW_URL}" >> $GITHUB_ENV
            
            # To avoid mistakes, let's don't allow prod deployment into development.
            if [[ "${{ inputs.environment || 'development' }}" != "production" ]]; then
              exit 1
            fi
          elif [[ "${INPUTS_SHORT_NAME}" == "staging" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="staging-latest"
            FE_URL="https://natures-nectar-dev.web.app"
          elif [[ "${INPUTS_SHORT_NAME}" == "PR" ]]; then
            RAW_NAME="pr-${{ github.event.pull_request.number }}-${{ github.event.pull_request.user.login }}"
            SHORT_NAME=$(echo "${RAW_NAME}" | tr '[:upper:]' '[:lower:]' | tr -c 'a-z0-9-' '-' | cut -c1-40)
            CHANNEL_ID="${SHORT_NAME}"
            TAG_NAME="${SHORT_NAME}"
            FE_URL=""
          else
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="${INPUTS_SHORT_NAME}"
            TAG_NAME="${INPUTS_SHORT_NAME}"
            FE_URL=""
          fi

          echo "SHORT_NAME=$SHORT_NAME" >> $GITHUB_ENV
          echo "TAG_NAME=$TAG_NAME" >> $GITHUB_ENV
          echo "CHANNEL_ID=$CHANNEL_ID" >> $GITHUB_ENV

          if [[ "$SHORT_NAME" == "staging" ]]; then
            MIN_INSTANCES=1
          else
            MIN_INSTANCES=0
          fi
          echo "MIN_INSTANCES=$MIN_INSTANCES" >> $GITHUB_ENV

          echo "GCP_PROJECT_ID=${{ vars.GCP_PROJECT_ID }}" >> $GITHUB_ENV
          GCP_PROJECT_NUMBER=$(gcloud projects describe ${{ vars.GCP_PROJECT_ID }} --format='value(projectNumber)')
          echo "GCP_PROJECT_NUMBER=${GCP_PROJECT_NUMBER}" >> $GITHUB_ENV
          echo "GCP_DB_PASSWORD_SECRET_NAME=DB_PASSWORD_$(echo "${{ vars.DB_INSTANCE }}" | tr '[:lower:]' '[:upper:]' | tr '-' '_')" >> $GITHUB_ENV

          echo "DB_NAME=palmyrapro-db-$SHORT_NAME" >> $GITHUB_ENV
          # echo "DB_HOST=$(gcloud sql instances describe ${{ vars.DB_INSTANCE }} --format=json | jq -r '.ipAddresses[] | select(.type == "PRIMARY") | .ipAddress')" >> $GITHUB_ENV
          echo "DB_HOST=/cloudsql/${DB_INSTANCE_CONNECTION_NAME}" >> $GITHUB_ENV
          echo "DB_PASSWORD=${{ secrets.DB_PASSWORD }}" >>  $GITHUB_ENV
          echo "DB_USER=${{ vars.DB_USER }}" >> $GITHUB_ENV
          echo "DB_PORT=${{ vars.DB_PORT }}" >> $GITHUB_ENV

          echo "FE_URL=${FE_URL}" >> $GITHUB_ENV
          echo "API_URL=https://$SHORT_NAME-cacao-api-${GCP_PROJECT_NUMBER}.${{ vars.GCP_REGION }}.run.app" >> $GITHUB_ENV

      - name: Install Cloud SQL Auth Proxy
        run: |
          curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.15.2/cloud-sql-proxy.linux.amd64
          chmod +x cloud-sql-proxy
          sudo mv cloud-sql-proxy /usr/local/bin/

      - name: Start Cloud SQL Proxy
        run: |
          cloud-sql-proxy $DB_INSTANCE_CONNECTION_NAME --credentials-file=$GOOGLE_APPLICATION_CREDENTIALS --port 5432 &

          # Wait and check if port is open
          for i in {1..10}; do
            echo "Checking if proxy is up (attempt $i)..."
            nc -z 127.0.0.1 5432 && break
            sleep 2
          done

          # Final check
          if ! nc -z 127.0.0.1 5432; then
            echo "❌ Cloud SQL Proxy failed to start."
            exit 1
          fi

          echo "✅ Cloud SQL Proxy is up and running."

      - name: Check if database exists
        id: check-db
        env:
          PGPASSWORD: ${{ env.DB_PASSWORD }}
        run: |
          DB_EXISTS=$(psql -h 127.0.0.1 -U $DB_USER -d postgres \
            -tAc "SELECT 1 FROM pg_database WHERE datname = '$DB_NAME'" | grep -q 1 && echo true || echo false)

          echo "exists=$DB_EXISTS" >> $GITHUB_OUTPUT

      - name: Create Cloud SQL schema
        if: steps.check-db.outputs.exists == 'false'
        run: |
          gcloud sql databases create $DB_NAME --instance=${{ vars.DB_INSTANCE }}

      - name: Run DB migrations / init scripts
        if: steps.check-db.outputs.exists == 'false'
        env:
          PGPASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/01-ddl.sql"
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/02-seeding.sql"

      # TODO: From here, should be in a external Deploy Frontend workflow and reuse it.
      - name: FE. Update Firebase config with dynamic service ID
        run: |
          # Replace placeholder with actual service ID
          sed -i "s/PLACEHOLDER_SERVICE_ID/$SHORT_NAME-cacao-api/g" apps/frontend/firebase.json

          # Verify the replacement
          cat apps/frontend/firebase.json

      - name: FE. Build
        env:
          NEXT_PUBLIC_BACKEND_URL: "${{ env.FE_URL }}/api"
          CI: "true"
          SKIP_TYPE_CHECK: "true"
          # Firebase configuration (mix of variables and secrets)
          NEXT_PUBLIC_FIREBASE_API_KEY: "${{ secrets.NEXT_PUBLIC_FIREBASE_API_KEY }}"
          NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: "${{ vars.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN }}"
          NEXT_PUBLIC_FIREBASE_PROJECT_ID: "${{ vars.NEXT_PUBLIC_FIREBASE_PROJECT_ID }}"
          NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: "${{ vars.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET }}"
          NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: "${{ vars.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}"
          NEXT_PUBLIC_FIREBASE_APP_ID: "${{ secrets.NEXT_PUBLIC_FIREBASE_APP_ID }}"
        run: |
          npm ci
          npm run generate --workspace=packages/api-specs
          npm run build --workspace=apps/frontend
          du -sh apps/frontend/dist/.

      - name: Deploy to Firebase Hosting
        id: deploy
        working-directory: apps/frontend
        env:
          NODE_OPTIONS: "--trace-deprecation"
        run: |
          npm install -g firebase-tools

          # Deploy
          TMP_OUTPUT=$(mktemp)

          EXIT_CODE=0
          if [ "$CHANNEL_ID" == "live" ]; then
            firebase deploy \
            --only hosting \
            --project="${{ vars.GCP_PROJECT_ID }}" \
            --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?

            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi

            # Extract and comment the PREVIEW URL
            TMP_PREVIEW_URL="https://${{ vars.GCP_PROJECT_ID }}.web.app/"

            echo "✅ Preview URL: $TMP_PREVIEW_URL"
            echo "PREVIEW_URL=$TMP_PREVIEW_URL" >> "$GITHUB_OUTPUT"

          else
            firebase hosting:channel:deploy "$CHANNEL_ID" \
              --project="${{ vars.GCP_PROJECT_ID }}" \
              --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?

            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi

            # Extract and comment the PREVIEW URL
            TMP_PREVIEW_URL=$(jq -r '.result[].url' ${TMP_OUTPUT})
            echo "✅ Preview URL: $TMP_PREVIEW_URL"
            echo "PREVIEW_URL=$TMP_PREVIEW_URL" >> "$GITHUB_OUTPUT"

            echo "FE_URL=${TMP_PREVIEW_URL}" >> $GITHUB_ENV
          fi

      - name: Comment on PR with preview URL
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ✅ Preview deployed to: ${{ steps.deploy.outputs.PREVIEW_URL }}

      - name: API. Build and Push Docker Image
        run: |
          docker build -t ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:$TAG_NAME -f ./apps/api/Dockerfile .
          docker push ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:$TAG_NAME

      - name: API. Deploy to Cloud Run
        run: |
          # Debug: Print environment variables
          # echo "=== Environment Variables ==="
          # echo "SHORT_NAME: $SHORT_NAME"
          # echo "DB_NAME: ${{ env.DB_NAME }}"
          # echo "METABASE_SITE_URL: ${{ vars.METABASE_SITE_URL }}"
          # echo "FRONTEND_URL: ${{ env.FE_URL }}"
          # echo "API_SERVICE: $SHORT_NAME-cacao-api"

          # Deploy with volume mount for Firebase secret
          gcloud run deploy $SHORT_NAME-cacao-api \
            --image ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:$TAG_NAME \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --execution-environment gen2 \
            --min-instances ${{ env.MIN_INSTANCES }} \
            --max-instances 2 \
            --concurrency 1000 \
            --allow-unauthenticated \
            --port 3000 \
            --add-cloudsql-instances $DB_INSTANCE_CONNECTION_NAME \
            --set-env-vars "DB_NAME=${{ env.DB_NAME }},DB_HOST=${{ env.DB_HOST }},DB_PORT=${{ vars.DB_PORT }},DB_USER=${{ vars.DB_USER }},DB_SSL_MODE=${{ vars.DB_SSL_MODE }}" \
            --set-env-vars "METABASE_SITE_URL=${{ vars.METABASE_SITE_URL }},ENV_SHORT_NAME=${{ env.SHORT_NAME }}" \
            --set-env-vars "FIREBASE_ADMIN_KEY_PATH=/secrets/FIREBASE_SERVICE_ACCOUNT_KEY,FIREBASE_AUTH_SECRET=${{ vars.FIREBASE_AUTH_SECRET }},FE_URL=${{ env.FE_URL }}" \
            --set-secrets "/secrets/FIREBASE_SERVICE_ACCOUNT_KEY=FIREBASE_SERVICE_ACCOUNT_KEY:latest,DB_PASSWORD=${{ env.GCP_DB_PASSWORD_SECRET_NAME }}:latest,METABASE_SECRET_KEY=METABASE_SECRET_KEY:latest" \
            --labels "env_short_name=${{ env.SHORT_NAME }}"

          # TODO: Better if Firebase is routing this service into a static context path
          gcloud run services add-iam-policy-binding $SHORT_NAME-cacao-api \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --member="allUsers" \
            --role="roles/run.invoker"

      - name: API. Comment on PR with endpoint URL
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ✅ Cacao API deployed to: "$API_URL"
