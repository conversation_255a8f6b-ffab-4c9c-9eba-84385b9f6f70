apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
    run.googleapis.com/urls: '["https://test-manual-cacao-api-377696815406.asia-northeast2.run.app"]'
  labels:
    cloud.googleapis.com/location: asia-northeast2
    env_short_name: test-manual
  name: test-manual-cacao-api
  namespace: '377696815406'
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: '2'
        run.googleapis.com/client-name: gcloud
        run.googleapis.com/client-version: 530.0.0
        run.googleapis.com/cloudsql-instances: cacao---dev:asia-northeast2:palmyra-pro-db-dev
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/startup-cpu-boost: 'true'
      labels:
        client.knative.dev/nonce: kryleejyiw
        env_short_name: test-manual
        run.googleapis.com/startupProbeType: Default
    spec:
      containerConcurrency: 1000
      containers:
      - env:
        - name: DB_NAME
          value: palmyrapro-db-test-cacao-env-final
        - name: DB_HOST
          value: /cloudsql/cacao---dev:asia-northeast2:palmyra-pro-db-dev
        - name: DB_PORT
          value: '5432'
        - name: DB_USER
          value: cacao_user
        - name: DB_SSL_MODE
          value: disable
        - name: METABASE_SITE_URL
          value: https://zengate-global.metabaseapp.com
        - name: ENV_SHORT_NAME
          value: test-manual
        - name: ALLOWED_ORIGIN
          value: https://cacao---dev.web.app
        - name: FIREBASE_ADMIN_KEY_PATH
          value: /secrets/FIREBASE_SERVICE_ACCOUNT_KEY
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: latest
              name: DB_PASSWORD_PALMYRA_PRO_DB_DEV
        - name: METABASE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              key: latest
              name: METABASE_SECRET_KEY
        - name: FIREBASE_SERVICE_ACCOUNT_KEY
          valueFrom:
            secretKeyRef:
              key: latest
              name: FIREBASE_SERVICE_ACCOUNT_KEY
        image: asia-northeast2-docker.pkg.dev/cacao---dev/palmyra-pro-images/cacao-api:test-cacao-env-final
        ports:
        - containerPort: 3000
          name: http1
        resources:
          limits:
            cpu: 1000m
            memory: 512Mi
        startupProbe:
          failureThreshold: 1
          periodSeconds: 240
          tcpSocket:
            port: 3000
          timeoutSeconds: 240
      serviceAccountName: <EMAIL>
      timeoutSeconds: 300
  traffic:
  - latestRevision: true
    percent: 100
