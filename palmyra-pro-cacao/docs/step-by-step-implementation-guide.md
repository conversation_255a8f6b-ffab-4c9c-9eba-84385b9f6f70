# Palmyra Pro Cacao - Step-by-Step Implementation Guide

## Overview
This document provides a complete step-by-step guide for setting up the development environment. Each step includes the exact commands used, expected outputs, and troubleshooting information.

**Project**: Palmyra Pro Cacao  
**Environment**: Development  
**Started**: 2025-01-23  
**Current Phase**: Environment Assessment & Firebase Setup

---

## Prerequisites

### Required Tools
- `gcloud` CLI installed and authenticated
- `firebase` CLI installed and authenticated
- `git` configured
- `node.js` and `npm` installed
- Access to GCP project: `cacao---dev`
- Access to Firebase project: `cacao---dev`

### Authentication Setup
```bash
# Authenticate with Google Cloud
gcloud auth login

# Authenticate with Firebase
firebase login

# Set default project contexts
gcloud config set project cacao---dev
firebase use cacao---dev
```

---

## Phase 1: Environment Assessment & Firebase Setup

### Step 1: Project Context Setup ✅

**Commands Executed:**
```bash
# Set GCP project context
gcloud config set project cacao---dev

# Set Firebase project context
firebase use cacao---dev

# Create working branch
git checkout -b feature/dev-environment-setup
```

**Expected Output:**
```
Updated property [core/project].
Now using project cacao---dev
Switched to a new branch 'feature/dev-environment-setup'
```

**Status**: ✅ Completed

### Step 2: Current State Assessment

**Commands to Execute:**
```bash
# Create assessment directory
mkdir -p docs/assessment
cd docs/assessment

# Firebase assessment
firebase projects:list > firebase-projects.txt
firebase hosting:sites:list > firebase-hosting.txt

# GCP assessment
gcloud services list --enabled > gcp-enabled-apis.txt
gcloud sql instances list > gcp-sql-instances.txt
gcloud artifacts repositories list > gcp-repositories.txt
gcloud iam service-accounts list > gcp-service-accounts.txt
gcloud secrets list > gcp-secrets.txt

# Repository assessment
ls -la ../../.github/workflows/ > workflow-files.txt
```

**Status**: 🔄 In Progress

### Step 3: Firebase Initialization ✅

**Commands Executed:**
```bash
cd palmyra-pro-cacao  # Root directory
firebase init
```

**Firebase Features Selected:**
```
✔ Hosting: Configure files for Firebase Hosting and (optionally) set up GitHub Action deploys
```

**Configuration Choices Made:**
- **Project**: `Use an existing project` → `cacao---dev (Cacao - Dev)`
- **Public Directory**: `dist`
- **Single-page app**: `Yes`
- **GitHub auto-deploy**: `No`

**Actual Output:**
```
✔ Which Firebase features do you want to set up for this directory? Hosting
✔ Please select an option: Use an existing project
✔ Select a default Firebase project for this directory: cacao---dev (Cacao - Dev)
✔ What do you want to use as your public directory? dist
✔ Configure as a single-page app (rewrite all urls to /index.html)? Yes
✔ Set up automatic builds and deploys with GitHub? No
✔ Wrote dist/index.html
✔ Wrote configuration info to firebase.json
✔ Wrote project information to .firebaserc
✔ Firebase initialization complete!
```

**Files Created:**
- `firebase.json` - Firebase hosting configuration
- `.firebaserc` - Project association
- `dist/index.html` - Default hosting file

**Firebase Project Information Discovered:**
```bash
firebase projects:list
```
**Output:**
```
┌──────────────────────┬───────────────────────┬────────────────┬──────────────────────┐
│ Project Display Name │ Project ID            │ Project Number │ Resource Location ID │
├──────────────────────┼───────────────────────┼────────────────┼──────────────────────┤
│ Cacao - Dev          │ cacao---dev (current) │ ************   │ [Not specified]      │
├──────────────────────┼───────────────────────┼────────────────┼──────────────────────┤
│ Natures Nectar - Dev │ natures-nectar-dev    │ 463523546      │ [Not specified]      │
├──────────────────────┼───────────────────────┼────────────────┼──────────────────────┤
│ Palmyra-Express-demo │ palmyra-express-demo  │ 234046346333   │ [Not specified]      │
└──────────────────────┴───────────────────────┴────────────────┴──────────────────────┘
```

**Firebase Hosting Sites:**
```bash
firebase hosting:sites:list
```
**Output:**
```
┌─────────────┬─────────────────────────────┬─────────────────┐
│ Site ID     │ Default URL                 │ App ID (if set) │
├─────────────┼─────────────────────────────┼─────────────────┤
│ cacao---dev │ https://cacao---dev.web.app │ --              │
└─────────────┴─────────────────────────────┴─────────────────┘
```

**Key Information Extracted:**
- **Firebase Project ID**: `cacao---dev`
- **Project Number**: `************`
- **Hosting URL**: `https://cacao---dev.web.app`
- **GCP Project**: Not yet linked (needs setup)

**Status**: ✅ Completed

### Step 4: Google Cloud Project Assessment ✅

**Status Update**: Both Firebase and GCP projects exist and are accessible from the same Google company account.

**Confirmed Setup:**
- ✅ Firebase project `cacao---dev` exists
- ✅ GCP project `cacao---dev` exists
- ✅ Both accessible from same Google account
- ✅ Projects are linked (Firebase + GCP)

**Next Actions**: Set up GCP infrastructure and enable required services

### Step 5: GCP Infrastructure Setup ✅

**Current Task**: Enable required GCP APIs and set up infrastructure

**Commands Executed:**

**Step 5.1: Set Project Context and Verify Access** ✅
```bash
gcloud config set project cacao---dev
gcloud projects describe cacao---dev
gcloud services list --enabled
```

**Results:**
- **Project ID**: `cacao---dev`
- **Project Number**: `************`
- **Organization**: `************`
- **Firebase Integration**: ✅ Enabled
- **Lifecycle State**: ACTIVE

**Key APIs Already Enabled:**
- ✅ `run.googleapis.com` - Cloud Run Admin API
- ✅ `sql-component.googleapis.com` - Cloud SQL
- ✅ `artifactregistry.googleapis.com` - Artifact Registry API
- ✅ `firebase.googleapis.com` - Firebase Management API
- ✅ `firebasehosting.googleapis.com` - Firebase Hosting API
- ✅ `cloudresourcemanager.googleapis.com` - Cloud Resource Manager API

**Step 5.2: Enable Required GCP APIs** ✅
```bash
gcloud services enable \
  run.googleapis.com \
  sql-component.googleapis.com \
  artifactregistry.googleapis.com \
  cloudbuild.googleapis.com \
  secretmanager.googleapis.com \
  cloudresourcemanager.googleapis.com \
  firebase.googleapis.com \
  firebasehosting.googleapis.com
```

**Result:**
```
Operation "operations/acf.p2-************-829a9f2b-8f54-42b1-bb48-5e6d4c476532" finished successfully.
```

**Status**: ✅ All required APIs enabled successfully

**Step 5.3: Check Current Infrastructure State** ✅
```bash
gcloud sql instances list
gcloud artifacts repositories list
gcloud iam service-accounts list
gcloud secrets list
gcloud run services list
```

**Results:**
- **Cloud SQL Instances**: 0 (none exist - need to create)
- **Artifact Registry Repositories**: 0 (none exist - need to create)
- **Secrets**: 0 (none exist - need to create)
- **Cloud Run Services**: 0 (none exist - will be created by deployment)

**Existing Service Accounts:**
- ✅ `<EMAIL>` (Default compute)
- ✅ `<EMAIL>` (Firebase Admin)

**Status**: ✅ Infrastructure assessment complete - clean slate for setup

### Step 6: Infrastructure Planning & Regional Setup 🔄

**Region Selection**: Asia-Northeast2 (Osaka, Japan)
**Current Task**: Plan all infrastructure components and create them in Osaka region

## Complete Infrastructure Services List

### **GCP Services to be Created:**

#### **1. Artifact Registry**
- **Service**: Docker repository for container images
- **Name**: `palmyra-pro-images`
- **Location**: `asia-northeast2` (Osaka)
- **Purpose**: Store Docker images for API and frontend

#### **2. Cloud SQL (PostgreSQL)**
- **Service**: Managed PostgreSQL database
- **Instance Name**: `palmyra-pro-db-dev`
- **Database Name**: `palmyrapro-db-dev`
- **User**: `palmyra_user`
- **Location**: `asia-northeast2` (Osaka)
- **Tier**: `db-f1-micro` (development)
- **Purpose**: Application database

#### **3. Secret Manager**
- **Secrets to create**:
  - `DB_PASSWORD_PALMYRA_PRO_DB_DEV` - Database password
  - `METABASE_SECRET_KEY` - Metabase integration key
- **Purpose**: Secure storage of sensitive configuration

#### **4. Service Accounts**
- **GitHub Actions Service Account**: `github-actions-dev`
  - Roles: Cloud Run Admin, SQL Admin, Artifact Registry Admin, Secret Accessor
- **Purpose**: CI/CD pipeline authentication

#### **5. Cloud Run Services** (Created by deployment)
- **API Service**: `dev-nn-api`
- **Location**: `asia-northeast2` (Osaka)
- **Purpose**: Go API backend hosting

### **Firebase Services (Already Configured):**

#### **1. Firebase Hosting**
- **Site ID**: `cacao---dev`
- **URL**: `https://cacao---dev.web.app`
- **CDN**: Global (automatically configured)
- **Purpose**: Frontend hosting with global CDN

#### **2. Firebase Authentication**
- **Project**: `cacao---dev`
- **Service Account**: `<EMAIL>`
- **Purpose**: User authentication and authorization

### **GitHub Actions Configuration:**
- **Repository Variables**: Project IDs, regions, database info
- **Repository Secrets**: Service account keys, passwords
- **Workflows**: Build, test, and deploy automation

## Regional Infrastructure Setup

**Step 6.1: Create Artifact Registry Repository (Osaka)**
```bash
# Create Docker repository for container images in Osaka
gcloud artifacts repositories create palmyra-pro-images \
  --repository-format=docker \
  --location=asia-northeast2 \
  --description="Docker images for Palmyra Pro Cacao Dev Environment - Osaka"

# Verify repository creation
gcloud artifacts repositories list --location=asia-northeast2
```

**Step 6.2: Create Cloud SQL Instance with Essential Requirements (Osaka)** ✅
```bash
# Create PostgreSQL instance with essential requirements
gcloud sql instances create palmyra-pro-db-dev \
  --database-version=POSTGRES_15 \
  --tier=db-f1-micro \
  --region=asia-northeast2 \
  --storage-type=SSD \
  --storage-size=10GB
```

**Step 6.2b: Create Database User** ✅
```bash
# Create database user (this user will access all databases)
gcloud sql users create cacao_user \
  --instance=palmyra-pro-db-dev \
  --password=cacao_dev_password_2025
```

**Output:**
```
Creating Cloud SQL user...done.
Created user [cacao_user].
```

**Step 6.2c: Create Application Database** ✅
```bash
# Create the development database inside the existing instance
gcloud sql databases create cacao-db-dev \
  --instance=palmyra-pro-db-dev
```

**Output:**
```
Creating Cloud SQL database...done.
Created database [cacao-db-dev].
instance: palmyra-pro-db-dev
name: cacao-db-dev
project: cacao---dev
```

**Step 6.2d: Verify Database Setup** ✅
```bash
# Verify database creation
gcloud sql databases list --instance=palmyra-pro-db-dev
```

**Output:**
```
NAME          CHARSET  COLLATION
postgres      UTF8     en_US.UTF8
cacao-db-dev  UTF8     en_US.UTF8
```

**Status**: ✅ Both default `postgres` database and our `cacao-db-dev` database are created successfully

**Step 6.2e: Verify User Creation** ✅
```bash
# Verify user creation
gcloud sql users list --instance=palmyra-pro-db-dev
```

**Output:**
```
NAME        HOST  TYPE      PASSWORD_POLICY
cacao_user        BUILT_IN
postgres          BUILT_IN
```

**Status**: ✅ Both default `postgres` user and our `cacao_user` are created successfully

**Essential Configuration Included:**
- ✅ **SSD storage** - Better performance than HDD
- ✅ **10GB storage** - Reasonable size for development
- ✅ **PostgreSQL 15** - Latest stable version
- ✅ **db-f1-micro** - Cost-effective for development

**Simplified (Removed non-essential):**
- ❌ **No backups** - Not critical for dev environment
- ❌ **No maintenance windows** - Default schedule is fine
- ❌ **No binary logging** - Not needed for development

**Step 6.3: Create Secrets in Secret Manager**
```bash
# Store database password
echo -n "palmyra_dev_password_2025" | gcloud secrets create DB_PASSWORD_PALMYRA_PRO_DB_DEV --data-file=-

# Store Metabase secret (using placeholder - update with real value)
echo -n "bdc8d3f820b8649e7fc2f8d662f515f4e4ae8fa2013d37c008e67a1d7a109802" | gcloud secrets create METABASE_SECRET_KEY --data-file=-

# Verify secrets creation
gcloud secrets list
```

**Step 6.4: Create Service Account for GitHub Actions**
```bash
# Create service account for GitHub Actions
gcloud iam service-accounts create github-actions-dev \
  --display-name="GitHub Actions Dev Environment" \
  --description="Service account for GitHub Actions CI/CD in dev environment"

# Grant required roles
gcloud projects add-iam-policy-binding cacao---dev \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/run.admin"

gcloud projects add-iam-policy-binding cacao---dev \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/sql.admin"

gcloud projects add-iam-policy-binding cacao---dev \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/artifactregistry.admin"

gcloud projects add-iam-policy-binding cacao---dev \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"

# Generate service account key
gcloud iam service-accounts keys create github-actions-dev-key.json \
  --iam-account=<EMAIL>
```

1. **✅ SELECT: Hosting**
   - **Why**: We need Firebase Hosting for frontend deployment with CDN
   - **Purpose**: Serves static Next.js build with global CDN
   - **Required**: Yes - Core requirement for our architecture

2. **❌ SKIP: Data Connect**
   - **Why**: We're using PostgreSQL on Cloud SQL, not Firebase Data Connect
   - **Purpose**: Would be for Firebase-native database connections
   - **Required**: No - We have our own database setup

3. **❌ SKIP: Firestore**
   - **Why**: We're using PostgreSQL, not Firestore
   - **Purpose**: NoSQL database with security rules
   - **Required**: No - Different database technology

4. **❌ SKIP: Genkit**
   - **Why**: We're not building an AI application with Genkit
   - **Purpose**: AI application development framework
   - **Required**: No - Not applicable to our use case

5. **❌ SKIP: Functions**
   - **Why**: We have a Go API backend, not Cloud Functions
   - **Purpose**: Serverless functions
   - **Required**: No - We use Cloud Run for backend

6. **❌ SKIP: App Hosting**
   - **Why**: We're using standard Firebase Hosting, not App Hosting
   - **Purpose**: Full-stack app deployment (newer feature)
   - **Required**: No - Standard hosting meets our needs

7. **❌ SKIP: Storage**
   - **Why**: We're not using Firebase Storage currently
   - **Purpose**: File storage with security rules
   - **Required**: No - Can be added later if needed

**Selection Instructions:**
1. Use arrow keys to navigate to "Hosting"
2. Press `<space>` to select Hosting (should show ◉)
3. Press `<enter>` to proceed with selection

**Next Expected Prompts After Selection:**

1. **"Please select an option:"**
   - Select: `Use an existing project`

2. **"Select a default Firebase project for this directory:"**
   - Select: `cacao---dev`

3. **"What do you want to use as your public directory?"**
   - Answer: `dist` (this matches our Next.js build output)

4. **"Configure as a single-page app (rewrite all urls to /index.html)?"**
   - Answer: `Yes` (required for Next.js routing)

5. **"Set up automatic builds and deploys with GitHub?"**
   - Answer: `No` (we'll configure this manually in our workflows)

6. **"File dist/index.html already exists. Overwrite?"**
   - Answer: `No` (if this appears, keep existing file)

---

## Expected Firebase Configuration

After initialization, the following files should be created/updated:

### `.firebaserc`
```json
{
  "projects": {
    "default": "cacao---dev"
  }
}
```

### `firebase.json` (Updated)
```json
{
  "hosting": {
    "public": "dist",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "/api/**",
        "run": {
          "serviceId": "dev-nn-api",
          "region": "us-central1"
        }
      },
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  }
}
```

---

## Troubleshooting

### Common Issues

1. **"Project not found" error**
   ```bash
   # Verify project access
   gcloud projects list
   firebase projects:list
   ```

2. **"Permission denied" error**
   ```bash
   # Re-authenticate
   gcloud auth login
   firebase login
   ```

3. **"Firebase project not initialized" error**
   ```bash
   # Ensure you're in the correct directory
   cd apps/frontend
   firebase use cacao---dev
   ```

---

## Next Steps After Firebase Init

1. **Verify Firebase Configuration**
   ```bash
   # Test Firebase configuration
   firebase hosting:sites:list
   ```

2. **Update Firebase Hosting Rewrites**
   - Manually update `firebase.json` with API rewrites
   - Configure for CORS elimination

3. **Test Local Firebase Hosting**
   ```bash
   # Build the application first
   npm run build
   
   # Test Firebase hosting locally
   firebase serve
   ```

---

## Progress Tracking

### Completed Steps
- ✅ Project context setup
- ✅ Working branch creation
- ✅ Firebase initialization
- ✅ Firebase hosting configuration
- ✅ Firebase project information gathering
- ✅ GCP project verification (both projects exist and accessible)
- ✅ GCP APIs enablement (Step 5.2)
- ✅ Infrastructure state assessment (Step 5.3)
- ✅ Artifact Registry creation (Step 6.1)
- ✅ Cloud SQL instance creation (Step 6.2)
- ✅ Database user creation (Step 6.2b)
- ✅ Application database creation (Step 6.2c)
- ✅ Artifact Registry repository creation (Step 6.1) - `palmyra-pro-images` in asia-northeast2
- ✅ Secret Manager setup (Step 6.3) - `DB_PASSWORD_CACAO_DB_DEV` and `METABASE_SECRET_KEY`
- ✅ GitHub Actions service account creation (Step 6.4) - `<EMAIL>`
- ✅ IAM roles assignment (Step 6.4b) - All required roles assigned to GitHub Actions service account
- ✅ Service account key generation (Step 6.4c) - `github-actions-dev-key.json` created
- ✅ GitHub CLI installation and authentication (Step 6.5) - `gh` configured for repository
- ✅ GitHub repository variables setup (Step 7) - 8/8 variables configured
- ✅ GitHub repository secrets setup (Step 8) - 2/2 secrets configured
- ✅ Database configuration verification (Step 9) - PostgreSQL 15 instance confirmed
- ✅ Workflow updates for Palmyra Pro Cacao (Step 10) - Updated app paths, service names, and environment variables

### Current Step
- 🔄 Ready for workflow testing

### Next Steps
- ⏳ Test workflow deployment with manual trigger
- ⏳ Verify PR environment creation
- ⏳ Test frontend and backend deployment
- ⏳ Validate database creation and seeding
- ⏳ End-to-end validation

---

## Notes and Decisions

### Firebase Hosting Choice Rationale
- **Hosting Only**: We only need hosting for static frontend deployment
- **No Functions**: Using Cloud Run for backend API
- **No Firestore**: Using PostgreSQL on Cloud SQL
- **No Storage**: Not currently needed, can add later

### Configuration Decisions
- **Public Directory**: `dist` (Next.js build output)
- **SPA Mode**: Yes (required for Next.js client-side routing)
- **GitHub Integration**: No (manual workflow configuration)

---

**Last Updated**: 2025-01-23  
**Current Status**: Firebase initialization in progress  
**Next Update**: After Firebase init completion
