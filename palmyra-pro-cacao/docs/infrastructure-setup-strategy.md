# Palmyra Pro Cacao - Infrastructure Setup Strategy

## Project Overview
**Project**: Palmyra Pro Cacao  
**Environment**: Development (Dev)  
**Date**: 2025-01-23  
**Status**: Planning Phase  

## Objective
Set up a complete development environment infrastructure on Google Cloud Platform and Firebase, ensuring:
- Minimal GCP services usage
- Firebase hosting with CDN for frontend
- Firebase redirects instead of direct GCP URLs
- Backend access through Firebase context hosting (avoiding CORS)
- Proper CI/CD pipeline with GitHub Actions

## Current State Assessment
- ✅ Firebase project exists for dev environment
- ✅ Google Cloud project exists for dev environment  
- ✅ Monorepo structure with Next.js frontend and Go API
- ✅ Docker configurations available
- ✅ GitHub Actions workflows (copied from another project)
- ❓ Need to validate existing configurations

## Architecture Overview

### Technology Stack
- **Frontend**: Next.js with Firebase Auth
- **Backend**: Go API with Firebase Admin SDK
- **Database**: PostgreSQL (Cloud SQL)
- **Hosting**: Firebase Hosting with Cloud CDN
- **Container Registry**: Google Artifact Registry
- **Compute**: Google Cloud Run
- **CI/CD**: GitHub Actions

### Service Architecture
```
[GitHub] → [GitHub Actions] → [GCP Artifact Registry] → [Cloud Run]
                           ↓
[Firebase Hosting] ← [Cloud CDN] ← [Users]
        ↓
[Cloud Run API] ← [Cloud SQL]
```

## Implementation Strategy

### Phase 1: Environment Assessment & Validation (Day 1)
**Objective**: Understand current state and validate existing resources

#### 1.1 Audit Existing Resources
- [ ] Validate Firebase project configuration
- [ ] Validate GCP project setup and enabled APIs
- [ ] Review current GitHub Actions workflows
- [ ] Document existing environment variables and secrets
- [ ] Assess current database schema and migrations

#### 1.2 Gap Analysis
- [ ] Identify missing GCP services
- [ ] Identify missing Firebase configurations
- [ ] Identify workflow modifications needed
- [ ] Document security and IAM requirements

#### 1.3 Risk Assessment
- [ ] Identify potential deployment blockers
- [ ] Document rollback strategies
- [ ] Plan testing approach

### Phase 2: GCP Infrastructure Configuration (Day 2-3)
**Objective**: Set up core GCP infrastructure components

#### 2.1 Enable Required APIs
```bash
gcloud services enable run.googleapis.com
gcloud services enable sql-component.googleapis.com
gcloud services enable artifactregistry.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable secretmanager.googleapis.com
```

#### 2.2 Artifact Registry Setup
- [ ] Create Docker repository for container images
- [ ] Configure authentication for GitHub Actions
- [ ] Test image push/pull functionality

#### 2.3 Cloud SQL Configuration
- [ ] Validate existing Cloud SQL instance or create new one
- [ ] Configure database users and permissions
- [ ] Set up Cloud SQL Proxy for secure connections
- [ ] Test database connectivity

#### 2.4 Secret Manager Setup
- [ ] Store database passwords securely
- [ ] Store Firebase service account keys
- [ ] Store Metabase secrets
- [ ] Configure IAM access for services

#### 2.5 IAM and Service Accounts
- [ ] Create GitHub Actions service account
- [ ] Create Firebase Admin service account
- [ ] Configure proper IAM roles and permissions
- [ ] Generate and secure service account keys

### Phase 3: Firebase Configuration & Integration (Day 4)
**Objective**: Configure Firebase services for optimal integration

#### 3.1 Firebase Hosting Setup
- [ ] Configure hosting for Next.js static export
- [ ] Set up Firebase hosting rewrites for API access
- [ ] Configure Cloud CDN integration
- [ ] Test static asset delivery

#### 3.2 Firebase Authentication
- [ ] Validate Firebase Auth configuration
- [ ] Set up tenant management if needed
- [ ] Configure authentication providers
- [ ] Test authentication flow

#### 3.3 Firebase Service Integration
- [ ] Configure Firebase Admin SDK for backend
- [ ] Set up service account authentication
- [ ] Test Firebase token verification
- [ ] Validate CORS elimination through rewrites

### Phase 4: Application Configuration Updates (Day 5)
**Objective**: Update application configurations for dev environment

#### 4.1 Frontend Configuration
- [ ] Create environment-specific configuration files
- [ ] Update Firebase client configuration
- [ ] Configure API base URLs for Firebase rewrites
- [ ] Update build process for static export

#### 4.2 Backend Configuration
- [ ] Update environment variables for dev deployment
- [ ] Configure Cloud SQL connection strings
- [ ] Set up Firebase Admin SDK integration
- [ ] Configure CORS policies

#### 4.3 Database Configuration
- [ ] Validate database schema migrations
- [ ] Set up dev-specific database seeding
- [ ] Configure connection pooling
- [ ] Test database operations

### Phase 5: GitHub Actions Workflow Updates (Day 6)
**Objective**: Update CI/CD pipeline for dev environment

#### 5.1 Environment Variables Configuration
- [ ] Set up GitHub repository variables for dev
- [ ] Configure GitHub repository secrets
- [ ] Update workflow environment mappings
- [ ] Test secret access

#### 5.2 Workflow Modifications
- [ ] Update Docker build and push steps
- [ ] Configure Cloud Run deployment
- [ ] Set up Firebase hosting deployment
- [ ] Add proper error handling and rollback

#### 5.3 Database Migration Integration
- [ ] Add database migration steps
- [ ] Configure Cloud SQL Proxy in workflows
- [ ] Set up conditional database seeding
- [ ] Test migration execution

### Phase 6: Testing & Validation (Day 7)
**Objective**: Comprehensive testing of the complete setup

#### 6.1 Unit Testing
- [ ] Test individual service deployments
- [ ] Validate database connections
- [ ] Test Firebase authentication
- [ ] Verify API endpoints

#### 6.2 Integration Testing
- [ ] Test complete user authentication flow
- [ ] Validate API access through Firebase hosting
- [ ] Test database operations end-to-end
- [ ] Verify CORS elimination

#### 6.3 Performance Testing
- [ ] Test CDN performance
- [ ] Validate API response times
- [ ] Check database query performance
- [ ] Monitor resource usage

#### 6.4 Security Testing
- [ ] Validate authentication and authorization
- [ ] Test service account permissions
- [ ] Verify secret management
- [ ] Check network security

### Phase 7: Documentation & Handover (Day 8)
**Objective**: Create comprehensive documentation and templates

#### 7.1 Technical Documentation
- [ ] Document complete infrastructure setup
- [ ] Create troubleshooting guides
- [ ] Document monitoring and alerting
- [ ] Create backup and recovery procedures

#### 7.2 Operational Documentation
- [ ] Create deployment runbooks
- [ ] Document environment management
- [ ] Create incident response procedures
- [ ] Document scaling procedures

#### 7.3 Template Creation
- [ ] Create staging environment templates
- [ ] Create production environment templates
- [ ] Document environment promotion process
- [ ] Create automation scripts

## Success Criteria

### Technical Success Criteria
- [ ] Complete dev environment deployed and functional
- [ ] All services accessible through Firebase hosting
- [ ] No CORS issues in API communication
- [ ] Database migrations working correctly
- [ ] CI/CD pipeline deploying successfully
- [ ] Authentication flow working end-to-end

### Operational Success Criteria
- [ ] Monitoring and alerting configured
- [ ] Backup and recovery tested
- [ ] Documentation complete and accessible
- [ ] Team trained on new infrastructure
- [ ] Staging/prod templates ready

## Risk Mitigation

### Technical Risks
- **Database Migration Failures**: Test migrations in isolated environment first
- **Authentication Issues**: Validate Firebase configuration before deployment
- **CORS Problems**: Test Firebase rewrites thoroughly
- **Performance Issues**: Monitor and optimize during testing phase

### Operational Risks
- **Deployment Downtime**: Use blue-green deployment strategies
- **Secret Management**: Use proper secret rotation and access controls
- **Cost Overruns**: Monitor resource usage and set up billing alerts
- **Security Vulnerabilities**: Regular security audits and updates

## Communication Plan

### Daily Standups
- Progress updates on current phase
- Blocker identification and resolution
- Next day planning

### Weekly Reviews
- Phase completion assessment
- Risk and issue review
- Timeline adjustments if needed

### Documentation Updates
- Real-time updates to this strategy document
- Technical decision logging
- Issue and resolution tracking

## Next Steps

1. **Immediate**: Begin Phase 1 - Environment Assessment
2. **Day 1 End**: Complete current state documentation
3. **Day 2 Start**: Begin GCP infrastructure configuration
4. **Weekly**: Review progress and adjust timeline as needed

---

**Document Version**: 1.0  
**Last Updated**: 2025-01-23  
**Next Review**: Daily during implementation
