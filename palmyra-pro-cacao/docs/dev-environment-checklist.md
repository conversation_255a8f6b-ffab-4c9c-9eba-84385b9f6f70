# Development Environment Setup Checklist

## Project Information
- **Project Name**: Palmyra Pro Cacao
- **Environment**: Development
- **Start Date**: 2025-01-23
- **Target Completion**: 2025-01-31 (8 days)

## Phase 1: Environment Assessment & Validation ⏳

### 1.1 Current State Audit
- [ ] **Firebase Project Validation**
  - [ ] Verify project ID: `cacao---dev`
  - [ ] Check enabled Firebase services (Auth, Hosting, etc.)
  - [ ] Validate current authentication configuration
  - [ ] Document existing users and tenants
  - [ ] Check hosting configuration and domains

- [ ] **GCP Project Validation**
  - [ ] Verify project ID matches Firebase project
  - [ ] List currently enabled APIs
  - [ ] Check existing Cloud SQL instances
  - [ ] Verify Artifact Registry repositories
  - [ ] Audit existing service accounts and IAM

- [ ] **GitHub Repository Audit**
  - [ ] Review existing workflow files
  - [ ] Check current environment variables
  - [ ] Validate existing secrets
  - [ ] Document workflow triggers and conditions

- [ ] **Application Configuration Review**
  - [ ] Review frontend environment variables
  - [ ] Check backend configuration files
  - [ ] Validate database schema files
  - [ ] Review Docker configurations

### 1.2 Gap Analysis Documentation
- [ ] **Missing GCP Services**
  - [ ] List required APIs not yet enabled
  - [ ] Identify missing infrastructure components
  - [ ] Document required service accounts

- [ ] **Missing Firebase Configuration**
  - [ ] Identify hosting configuration gaps
  - [ ] Check authentication setup completeness
  - [ ] Validate service account integration

- [ ] **Workflow Modifications Needed**
  - [ ] Document required environment variables
  - [ ] Identify workflow steps to modify
  - [ ] Plan secret management updates

### 1.3 Risk Assessment
- [ ] **Technical Risks**
  - [ ] Database migration compatibility
  - [ ] Authentication flow disruption
  - [ ] CORS configuration issues
  - [ ] Performance impact assessment

- [ ] **Operational Risks**
  - [ ] Deployment downtime potential
  - [ ] Secret exposure risks
  - [ ] Cost implications
  - [ ] Security vulnerabilities

**Phase 1 Completion Criteria**: ✅ Complete documentation of current state and required changes

---

## Phase 2: GCP Infrastructure Configuration ⏳

### 2.1 API Enablement
- [ ] **Enable Required APIs**
  ```bash
  gcloud services enable run.googleapis.com
  gcloud services enable sql-component.googleapis.com  
  gcloud services enable artifactregistry.googleapis.com
  gcloud services enable cloudbuild.googleapis.com
  gcloud services enable secretmanager.googleapis.com
  gcloud services enable cloudresourcemanager.googleapis.com
  ```
- [ ] **Verify API Status**
  - [ ] Confirm all APIs are enabled
  - [ ] Check API quotas and limits
  - [ ] Document any API restrictions

### 2.2 Artifact Registry Setup
- [ ] **Create Docker Repository**
  ```bash
  gcloud artifacts repositories create palmyra-pro-images \
    --repository-format=docker \
    --location=us-central1 \
    --description="Docker images for Palmyra Pro Dev"
  ```
- [ ] **Configure Authentication**
  - [ ] Set up Docker authentication
  - [ ] Test image push/pull access
  - [ ] Configure GitHub Actions access

### 2.3 Cloud SQL Configuration
- [ ] **Database Instance Setup**
  - [ ] Verify existing instance or create new
  - [ ] Configure instance settings (tier, region, storage)
  - [ ] Set up automated backups
  - [ ] Configure maintenance windows

- [ ] **Database and User Setup**
  - [ ] Create application database
  - [ ] Create database user with proper permissions
  - [ ] Configure SSL requirements
  - [ ] Test database connectivity

- [ ] **Cloud SQL Proxy Setup**
  - [ ] Install and configure proxy
  - [ ] Test local connections
  - [ ] Configure for GitHub Actions

### 2.4 Secret Manager Configuration
- [ ] **Create Required Secrets**
  - [ ] Database password: `DB_PASSWORD_PALMYRA_PRO_DB_DEV`
  - [ ] Metabase secret: `METABASE_SECRET_KEY`
  - [ ] Firebase service account key
  - [ ] GitHub Actions service account key

- [ ] **Configure Secret Access**
  - [ ] Set up IAM permissions for secret access
  - [ ] Test secret retrieval
  - [ ] Configure automatic rotation if needed

### 2.5 IAM and Service Accounts
- [ ] **GitHub Actions Service Account**
  ```bash
  gcloud iam service-accounts create github-actions-dev \
    --display-name="GitHub Actions Dev Environment"
  ```
  - [ ] Grant required roles (Cloud Run Admin, SQL Admin, etc.)
  - [ ] Generate and download service account key
  - [ ] Store key securely in GitHub secrets

- [ ] **Firebase Admin Service Account**
  - [ ] Create service account for API Firebase integration
  - [ ] Grant Firebase Admin role
  - [ ] Generate service account key
  - [ ] Configure for API usage

**Phase 2 Completion Criteria**: ✅ All GCP infrastructure components configured and tested

---

## Phase 3: Firebase Configuration & Integration ⏳

### 3.1 Firebase Hosting Setup
- [ ] **Hosting Configuration**
  - [ ] Update `firebase.json` with proper rewrites
  - [ ] Configure static asset caching
  - [ ] Set up custom domain (if applicable)
  - [ ] Test hosting deployment

- [ ] **CDN Configuration**
  - [ ] Verify Cloud CDN integration
  - [ ] Configure cache policies
  - [ ] Test global content delivery
  - [ ] Monitor performance metrics

### 3.2 Firebase Authentication
- [ ] **Authentication Setup**
  - [ ] Verify authentication providers
  - [ ] Configure tenant management
  - [ ] Set up custom claims if needed
  - [ ] Test authentication flow

- [ ] **Security Configuration**
  - [ ] Configure authorized domains
  - [ ] Set up security rules
  - [ ] Configure session management
  - [ ] Test token validation

### 3.3 Firebase Service Integration
- [ ] **Backend Integration**
  - [ ] Configure Firebase Admin SDK
  - [ ] Set up service account authentication
  - [ ] Test token verification
  - [ ] Validate user management

- [ ] **API Rewrites Configuration**
  - [ ] Configure Firebase hosting rewrites for API
  - [ ] Test API access through Firebase hosting
  - [ ] Verify CORS elimination
  - [ ] Test authentication flow through rewrites

**Phase 3 Completion Criteria**: ✅ Firebase services configured and integrated with backend

---

## Phase 4: Application Configuration Updates ⏳

### 4.1 Frontend Configuration
- [ ] **Environment Files**
  - [ ] Create `.env.development` with dev-specific variables
  - [ ] Update Firebase configuration for dev environment
  - [ ] Configure API base URL for Firebase rewrites
  - [ ] Test environment variable loading

- [ ] **Build Configuration**
  - [ ] Update Next.js configuration for static export
  - [ ] Configure build process for Firebase hosting
  - [ ] Test build and export process
  - [ ] Verify static asset generation

### 4.2 Backend Configuration
- [ ] **Environment Variables**
  - [ ] Update database connection strings
  - [ ] Configure Firebase Admin SDK settings
  - [ ] Set up Cloud SQL connection
  - [ ] Configure CORS policies

- [ ] **Service Integration**
  - [ ] Test Firebase token verification
  - [ ] Validate database connections
  - [ ] Test API endpoints
  - [ ] Verify authentication middleware

### 4.3 Database Configuration
- [ ] **Schema Management**
  - [ ] Validate migration scripts
  - [ ] Test database schema creation
  - [ ] Configure dev data seeding
  - [ ] Test rollback procedures

- [ ] **Connection Configuration**
  - [ ] Configure connection pooling
  - [ ] Set up SSL connections
  - [ ] Test connection reliability
  - [ ] Monitor connection metrics

**Phase 4 Completion Criteria**: ✅ All applications configured for dev environment

---

## Phase 5: GitHub Actions Workflow Updates ⏳

### 5.1 Environment Variables
- [ ] **Repository Variables Setup**
  - [ ] `GCP_PROJECT_ID`: Project identifier
  - [ ] `GCP_REGION`: Deployment region
  - [ ] `DB_INSTANCE`: Database instance name
  - [ ] `DB_USER`: Database username
  - [ ] Other required variables

- [ ] **Repository Secrets Setup**
  - [ ] `GCP_SERVICE_ACCOUNT_KEY`: Service account JSON
  - [ ] `DB_PASSWORD`: Database password
  - [ ] Other sensitive configuration

### 5.2 Workflow Modifications
- [ ] **Build and Deploy Steps**
  - [ ] Update Docker build configuration
  - [ ] Configure Cloud Run deployment
  - [ ] Set up Firebase hosting deployment
  - [ ] Add proper error handling

- [ ] **Database Migration Steps**
  - [ ] Add Cloud SQL Proxy setup
  - [ ] Configure migration execution
  - [ ] Set up conditional seeding
  - [ ] Test migration rollback

### 5.3 Testing and Validation
- [ ] **Workflow Testing**
  - [ ] Test manual workflow dispatch
  - [ ] Test PR-based deployments
  - [ ] Validate environment variable access
  - [ ] Test secret management

**Phase 5 Completion Criteria**: ✅ GitHub Actions workflows updated and tested

---

## Phase 6: Testing & Validation ⏳

### 6.1 Component Testing
- [ ] **Individual Service Tests**
  - [ ] Frontend deployment and access
  - [ ] Backend API functionality
  - [ ] Database connectivity
  - [ ] Authentication service

### 6.2 Integration Testing
- [ ] **End-to-End Testing**
  - [ ] Complete user authentication flow
  - [ ] API access through Firebase hosting
  - [ ] Database operations
  - [ ] CORS validation

### 6.3 Performance Testing
- [ ] **Performance Validation**
  - [ ] CDN performance metrics
  - [ ] API response times
  - [ ] Database query performance
  - [ ] Resource utilization

**Phase 6 Completion Criteria**: ✅ All tests passing and performance validated

---

## Phase 7: Documentation & Handover ⏳

### 7.1 Technical Documentation
- [ ] **Infrastructure Documentation**
  - [ ] Complete setup procedures
  - [ ] Configuration details
  - [ ] Troubleshooting guides
  - [ ] Monitoring setup

### 7.2 Operational Documentation
- [ ] **Process Documentation**
  - [ ] Deployment procedures
  - [ ] Incident response
  - [ ] Backup and recovery
  - [ ] Scaling procedures

### 7.3 Template Creation
- [ ] **Environment Templates**
  - [ ] Staging environment template
  - [ ] Production environment template
  - [ ] Automation scripts
  - [ ] Configuration templates

**Phase 7 Completion Criteria**: ✅ Complete documentation and templates ready

---

## Overall Success Criteria

### Technical Success
- [ ] Dev environment fully functional
- [ ] All services accessible through Firebase hosting
- [ ] No CORS issues
- [ ] CI/CD pipeline working
- [ ] Authentication flow complete

### Operational Success
- [ ] Monitoring configured
- [ ] Documentation complete
- [ ] Team trained
- [ ] Templates ready for staging/prod

---

**Checklist Version**: 1.0  
**Last Updated**: 2025-01-23  
**Progress**: Phase 1 - Environment Assessment
