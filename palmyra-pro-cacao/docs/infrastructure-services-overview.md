# Palmyra Pro Cacao - Infrastructure Services Overview

## Project Information
- **Environment**: Development
- **Region**: Asia-Northeast2 (Osaka, Japan)
- **GCP Project**: `cacao---dev`
- **Firebase Project**: `cacao---dev`
- **Project Number**: `************`

---

## Complete Services List

### 🔥 **Firebase Services**

#### 1. Firebase Hosting
- **Service**: Static website hosting with global CDN
- **Site ID**: `cacao---dev`
- **URL**: `https://cacao---dev.web.app`
- **Public Directory**: `dist` (Next.js build output)
- **Configuration**: Single-page app with API rewrites
- **CDN**: Global edge locations (automatic)
- **Purpose**: Frontend hosting with optimal performance worldwide

#### 2. Firebase Authentication
- **Service**: User authentication and authorization
- **Project**: `cacao---dev`
- **Features**: Email/password, tenant management
- **Integration**: Frontend (Next.js) + Backend (Go API)
- **Service Account**: `<EMAIL>`
- **Purpose**: Secure user authentication across the application

---

### ☁️ **Google Cloud Platform Services**

#### 1. Artifact Registry
- **Service**: Docker container registry
- **Repository Name**: `palmyra-pro-images`
- **Location**: `asia-northeast2` (Osaka)
- **Format**: Docker
- **URL**: `asia-northeast2-docker.pkg.dev/cacao---dev/palmyra-pro-images`
- **Purpose**: Store and manage Docker images for API and frontend
- **Images to Store**:
  - `nn-api:dev` - Go API backend
  - `nn-api:staging` - Staging API
  - `nn-api:latest` - Production API

#### 2. Cloud SQL (PostgreSQL) - Essential Requirements
- **Service**: Managed PostgreSQL database
- **Instance Name**: `palmyra-pro-db-dev`
- **Database Name**: `palmyrapro-db-dev`
- **Version**: PostgreSQL 15
- **Tier**: `db-f1-micro` (1 vCPU, 0.6 GB RAM)
- **Region**: `asia-northeast2` (Osaka)
- **Storage**: 10GB SSD (essential for performance)
- **Backup**: None (dev environment)
- **Maintenance**: Default schedule
- **Connection**: Cloud SQL Proxy for secure access
- **Purpose**: Development database with essential performance

#### 3. Cloud Run
- **Service**: Serverless container hosting
- **Service Name**: `dev-nn-api`
- **Region**: `asia-northeast2` (Osaka)
- **Image Source**: Artifact Registry
- **Scaling**: 0-2 instances (auto-scaling)
- **CPU**: 1 vCPU
- **Memory**: 512 MB
- **Concurrency**: 1000 requests per instance
- **Purpose**: Host Go API backend with automatic scaling

#### 4. Secret Manager
- **Service**: Secure configuration storage
- **Secrets**:
  - `DB_PASSWORD_PALMYRA_PRO_DB_DEV` - Database password
  - `METABASE_SECRET_KEY` - Metabase integration key
- **Access**: Service accounts with least privilege
- **Purpose**: Secure storage of sensitive configuration data

#### 5. Service Accounts & IAM
- **GitHub Actions Service Account**:
  - Name: `<EMAIL>`
  - Roles: Cloud Run Admin, SQL Admin, Artifact Registry Admin, Secret Accessor
  - Purpose: CI/CD pipeline authentication
- **Firebase Admin Service Account**:
  - Name: `<EMAIL>`
  - Purpose: Backend Firebase integration

---

### 🔄 **CI/CD Pipeline (GitHub Actions)**

#### 1. Workflow Services
- **Repository**: GitHub repository with Actions
- **Triggers**: PR creation, manual dispatch
- **Environment Variables**:
  - `GCP_PROJECT_ID`: `cacao---dev`
  - `GCP_REGION`: `asia-northeast2`
  - `DB_INSTANCE`: `palmyra-pro-db-dev`
  - `DB_USER`: `palmyra_user`
  - `DB_PORT`: `5432`
- **Secrets**:
  - `GCP_SERVICE_ACCOUNT_KEY`: GitHub Actions service account JSON
  - `DB_PASSWORD`: Database password

#### 2. Deployment Process
1. **Build**: Docker images built and pushed to Artifact Registry
2. **Database**: Migrations run via Cloud SQL Proxy
3. **Backend**: API deployed to Cloud Run
4. **Frontend**: Static build deployed to Firebase Hosting
5. **Testing**: End-to-end validation

---

### 🌐 **Network Architecture**

#### 1. Traffic Flow
```
User Request → Firebase Hosting (Global CDN) → API Rewrites → Cloud Run (Osaka) → Cloud SQL (Osaka)
```

#### 2. API Access
- **Frontend to API**: Via Firebase Hosting rewrites (`/api/**`)
- **Direct API Access**: `https://dev-nn-api-************.asia-northeast2.run.app`
- **Database Access**: Cloud SQL Proxy (secure tunnel)

#### 3. CORS Elimination
- Firebase Hosting rewrites eliminate CORS issues
- API appears to be served from same domain as frontend
- Secure authentication token passing

---

### 💰 **Cost Estimation (Monthly)**

#### Firebase Services
- **Hosting**: Free tier (up to 10GB storage, 10GB transfer)
- **Authentication**: Free tier (up to 50,000 MAU)

#### GCP Services (Osaka Region)
- **Cloud SQL (db-f1-micro)**: ~$7-10/month
- **Cloud Run**: Pay-per-use (~$0-5/month for dev)
- **Artifact Registry**: $0.10/GB/month (~$1/month)
- **Secret Manager**: $0.06 per 10,000 operations (~$0.50/month)

**Total Estimated Cost**: $8-16/month for development environment

---

### 🔒 **Security Features**

#### 1. Authentication & Authorization
- Firebase Authentication for user management
- Service account-based service authentication
- IAM roles with least privilege principle

#### 2. Network Security
- HTTPS everywhere (Firebase Hosting, Cloud Run)
- Private database connections via Cloud SQL Proxy
- VPC-native networking for Cloud Run

#### 3. Data Security
- Encrypted data at rest (Cloud SQL, Secret Manager)
- Encrypted data in transit (TLS 1.2+)
- Regular automated backups

---

### 📊 **Monitoring & Observability**

#### 1. Built-in Monitoring
- **Cloud Run**: Request metrics, error rates, latency
- **Cloud SQL**: Connection metrics, query performance
- **Firebase Hosting**: Traffic, bandwidth usage
- **Artifact Registry**: Image pulls, storage usage

#### 2. Logging
- **Cloud Run**: Application logs, request logs
- **Cloud SQL**: Query logs, error logs
- **Firebase**: Authentication logs, hosting logs

---

### 🚀 **Scaling Strategy**

#### 1. Horizontal Scaling
- **Cloud Run**: Auto-scales 0-2 instances based on traffic
- **Firebase Hosting**: Global CDN handles traffic spikes
- **Cloud SQL**: Can upgrade instance size as needed

#### 2. Performance Optimization
- **CDN**: Global edge caching for static assets
- **Database**: Connection pooling, query optimization
- **API**: Stateless design for easy scaling

---

## Next Steps After Infrastructure Creation

1. **Configure GitHub Actions**: Set up repository variables and secrets
2. **Update Application Configuration**: Environment-specific settings
3. **Deploy Applications**: First deployment via CI/CD pipeline
4. **Test End-to-End**: Validate complete application flow
5. **Set up Monitoring**: Configure alerts and dashboards

---

**Document Version**: 1.0  
**Created**: 2025-01-23  
**Region**: Asia-Northeast2 (Osaka)  
**Environment**: Development
